# Watchlist API Documentation

## Overview

The Watchlist API provides endpoints to manage user movie watchlists. Users can add movies to their watchlist, remove movies, and retrieve their watchlist with filtering and pagination support.

## Authentication

All endpoints require Firebase authentication. Include the Firebase ID token in the Authorization header:

```
Authorization: Bearer <firebase-id-token>
```

## Base URL

```
https://api-gateway-url/stage/watchlist
```

## Endpoints

### 1. Add Movie to Watchlist

Add a movie to the user's watchlist.

**Endpoint:** `POST /watchlist`

**Request Body:**
```json
{
  "name": "The Matrix",
  "movieId": "603",
  "status": "watch",
  "liked": false,
  "priorityTag": "mostWanted"
}
```

**Request Schema:**
- `name` (string, required): Movie name
- `movieId` (string, required): Movie ID
- `status` (string, required): Status - "watch" or "watched"
- `liked` (boolean, optional): Whether the movie is liked
- `priorityTag` (string, optional): Priority tag - "mostWanted", "watchNext", or "someday"

**Response (201 Created):**
```json
{
  "success": true,
  "message": "Movie added to watchlist successfully",
  "data": {
    "id": "firebase-uid#603",
    "firebaseUid": "firebase-uid",
    "name": "The Matrix",
    "movieId": "603",
    "status": "watch",
    "liked": false,
    "priorityTag": "mostWanted",
    "createdAt": "2024-01-15T10:30:00.000Z",
    "updatedAt": "2024-01-15T10:30:00.000Z"
  }
}
```

**Error Responses:**
- `400 Bad Request`: Invalid request body or missing required fields
- `401 Unauthorized`: Missing or invalid Firebase token
- `409 Conflict`: Movie already exists in watchlist
- `500 Internal Server Error`: Server error

### 2. Remove Movie from Watchlist

Remove a movie from the user's watchlist.

**Endpoint:** `DELETE /watchlist/{movieId}`

**Path Parameters:**
- `movieId` (string, required): Movie ID to remove

**Response (200 OK):**
```json
{
  "success": true,
  "message": "Movie removed from watchlist successfully"
}
```

**Error Responses:**
- `400 Bad Request`: Invalid movieId
- `401 Unauthorized`: Missing or invalid Firebase token
- `404 Not Found`: Movie not found in watchlist
- `500 Internal Server Error`: Server error

### 3. Get User's Watchlist

Retrieve the user's watchlist with optional filtering and pagination.

**Endpoint:** `GET /watchlist`

**Query Parameters:**
- `status` (string, optional): Filter by status - "watch" or "watched"
- `priorityTag` (string, optional): Filter by priority tag - "mostWanted", "watchNext", or "someday"
- `limit` (number, optional): Number of items to return (1-100, default: 20)
- `lastEvaluatedKey` (string, optional): Pagination token for next page

**Example Request:**
```
GET /watchlist?status=watch&priorityTag=mostWanted&limit=10
```

**Response (200 OK):**
```json
{
  "success": true,
  "data": [
    {
      "id": "firebase-uid#603",
      "firebaseUid": "firebase-uid",
      "name": "The Matrix",
      "movieId": "603",
      "status": "watch",
      "liked": false,
      "priorityTag": "mostWanted",
      "createdAt": "2024-01-15T10:30:00.000Z",
      "updatedAt": "2024-01-15T10:30:00.000Z"
    }
  ],
  "count": 1,
  "lastEvaluatedKey": "eyJpZCI6ImZpcmViYXNlLXVpZCM2MDMifQ==",
  "hasMore": false
}
```

**Response Schema:**
- `success` (boolean): Operation success status
- `data` (array): Array of watchlist items
- `count` (number): Number of items returned
- `lastEvaluatedKey` (string, optional): Pagination token for next page
- `hasMore` (boolean): Whether more items are available

**Error Responses:**
- `400 Bad Request`: Invalid query parameters
- `401 Unauthorized`: Missing or invalid Firebase token
- `500 Internal Server Error`: Server error

## Data Models

### Watchlist Item

```typescript
interface IWatchlist {
  id: string;                    // Composite key: firebaseUid#movieId
  firebaseUid: string;           // Firebase UID
  name: string;                  // Movie name
  movieId: string;               // Movie ID
  createdAt: string;             // ISO timestamp
  updatedAt: string;             // ISO timestamp
  status: 'watch' | 'watched';   // Watchlist status
  liked?: boolean;               // Optional liked flag
  priorityTag?: 'mostWanted' | 'watchNext' | 'someday'; // Optional priority
}
```

## Status Codes

- `200 OK`: Request successful
- `201 Created`: Resource created successfully
- `400 Bad Request`: Invalid request
- `401 Unauthorized`: Authentication required
- `404 Not Found`: Resource not found
- `409 Conflict`: Resource already exists
- `500 Internal Server Error`: Server error

## Rate Limiting

API requests are subject to AWS API Gateway rate limiting. Standard limits apply per user.

## Examples

### Adding a Movie to Watchlist

```bash
curl -X POST https://api-gateway-url/stage/watchlist \
  -H "Authorization: Bearer <firebase-token>" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Inception",
    "movieId": "27205",
    "status": "watch",
    "priorityTag": "watchNext"
  }'
```

### Getting Watchlist with Filters

```bash
curl -X GET "https://api-gateway-url/stage/watchlist?status=watch&limit=5" \
  -H "Authorization: Bearer <firebase-token>"
```

### Removing a Movie

```bash
curl -X DELETE https://api-gateway-url/stage/watchlist/27205 \
  -H "Authorization: Bearer <firebase-token>"
```
