#!/bin/bash

# Universal Deployment Script for ThyView
# Usage: 
#   ./scripts/deploy.sh <stage>                    # Deploy full stack
#   ./scripts/deploy.sh <stage> <function-name>    # Deploy single function
# Examples:
#   ./scripts/deploy.sh stage                      # Deploy full stack to stage
#   ./scripts/deploy.sh stage movieSearch          # Deploy only movieSearch to stage
#   ./scripts/deploy.sh prod createPost            # Deploy only createPost to prod

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    printf "${BLUE}[INFO]${NC} %s\n" "$1"
}

print_success() {
    printf "${GREEN}[SUCCESS]${NC} %s\n" "$1"
}

print_warning() {
    printf "${YELLOW}[WARNING]${NC} %s\n" "$1"
}

print_error() {
    printf "${RED}[ERROR]${NC} %s\n" "$1"
}

# Check parameters
if [ -z "$1" ]; then
    print_error "Stage is required!"
    echo "Usage:"
    echo "  $0 <stage>                    # Deploy full stack"
    echo "  $0 <stage> <function-name>    # Deploy single function"
    echo ""
    echo "Examples:"
    echo "  $0 stage                      # Deploy full stack to stage"
    echo "  $0 stage movieSearch          # Deploy only movieSearch to stage"
    echo "  $0 prod createPost            # Deploy only createPost to prod"
    exit 1
fi

STAGE=$1
FUNCTION_NAME=$2

# Validate stage
VALID_STAGES=("dev" "stage" "prod")
if [[ ! " ${VALID_STAGES[@]} " =~ " ${STAGE} " ]]; then
    print_error "Invalid stage: $STAGE"
    print_warning "Valid stages: ${VALID_STAGES[*]}"
    exit 1
fi

# Get script directory and navigate to project root
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
cd "$PROJECT_ROOT"

print_status "Project root: $PROJECT_ROOT"

# Check prerequisites
if ! command -v serverless &> /dev/null; then
    print_error "Serverless Framework is not installed!"
    print_warning "Install it with: npm install -g serverless"
    exit 1
fi

if ! aws sts get-caller-identity &> /dev/null; then
    print_error "AWS CLI is not configured or credentials are invalid!"
    print_warning "Configure AWS CLI with: aws configure"
    exit 1
fi

# Build the project
print_status "Building the project..."
cd functions
if ! npm run build; then
    print_error "Build failed!"
    exit 1
fi
cd ..

# Determine deployment type
if [ -z "$FUNCTION_NAME" ]; then
    # Full stack deployment
    print_status "Deploying full stack to $STAGE..."
    DEPLOY_CMD="serverless deploy --stage $STAGE"
    
    print_status "Running: $DEPLOY_CMD"
    if eval $DEPLOY_CMD; then
        print_success "Full stack deployed successfully to $STAGE!"
        print_status "Getting deployment info..."
        serverless info --stage $STAGE
    else
        print_error "Full stack deployment failed!"
        exit 1
    fi
else
    # Single function deployment
    print_status "Deploying function '$FUNCTION_NAME' to $STAGE..."
    
    # Check if stack exists
    STACK_NAME="thyview-api-$STAGE"
    if aws cloudformation describe-stacks --stack-name "$STACK_NAME" --region us-east-1 &> /dev/null; then
        print_status "Stack exists. Checking if function exists in stack..."

        # Check if function exists in the deployed stack
        if aws lambda get-function --function-name "$FUNCTION_NAME-$STAGE" --region us-east-1 &> /dev/null; then
            print_status "Function exists. Updating function code..."
            DEPLOY_CMD="serverless deploy function --function $FUNCTION_NAME --stage $STAGE"

            print_status "Running: $DEPLOY_CMD"
            if eval $DEPLOY_CMD; then
                print_success "Function '$FUNCTION_NAME' updated successfully to $STAGE!"

                # Show function info
                print_status "Function information:"
                serverless info --stage $STAGE | grep -A 5 -B 5 "$FUNCTION_NAME" || serverless info --stage $STAGE

                # Show useful commands
                print_status "Useful commands:"
                echo "  View logs: serverless logs --function $FUNCTION_NAME --stage $STAGE --tail"
                echo "  Test function: serverless invoke --function $FUNCTION_NAME --stage $STAGE"
                echo "  Function name in AWS: $FUNCTION_NAME-$STAGE"
            else
                print_error "Function update failed!"
                exit 1
            fi
        else
            print_warning "Function doesn't exist in current stack. Adding function to existing stack..."
            # Get list of existing functions in the stack
            EXISTING_FUNCTIONS=$(aws lambda list-functions --region us-east-1 --query "Functions[?starts_with(FunctionName, \`thyview-api-$STAGE-\`) || ends_with(FunctionName, \`-$STAGE\`)].FunctionName" --output text)
            print_status "Existing functions in stack: $EXISTING_FUNCTIONS"

            # Fall through to create temporary config that includes existing + new function
        fi
    fi

    # If we reach here, we need to create/update the stack with temporary config
    if [ "$DEPLOY_CMD" = "" ]; then
        if aws cloudformation describe-stacks --stack-name "$STACK_NAME" --region us-east-1 &> /dev/null; then
            print_warning "Adding function to existing stack using temporary config..."
        else
            print_warning "Stack doesn't exist. Creating minimal infrastructure with only $FUNCTION_NAME function..."
        fi

        # Create a temporary serverless config with the specified function(s)
        TEMP_CONFIG="serverless-temp-$FUNCTION_NAME.yml"

        print_status "Creating temporary configuration for $FUNCTION_NAME..."

        # Extract function configuration from main serverless.yml
        print_status "Extracting function configuration from serverless.yml..."

        # Check if PyYAML is installed, if not install it
        if ! python3 -c "import yaml" 2>/dev/null; then
            print_status "Installing PyYAML..."
            pip3 install PyYAML >/dev/null 2>&1 || {
                print_error "Failed to install PyYAML. Please install it manually: pip3 install PyYAML"
                exit 1
            }
        fi

        # Create a Python script to extract function configuration
        python3 << EOF
import yaml
import sys
import subprocess
import json

# Read the main serverless.yml
with open('serverless.yml', 'r') as f:
    config = yaml.safe_load(f)

function_name = "$FUNCTION_NAME"
stage = "$STAGE"

# Check if function exists
if function_name not in config.get('functions', {}):
    print(f"ERROR: Function '{function_name}' not found in serverless.yml")
    sys.exit(1)

# Get function configuration
func_config = config['functions'][function_name]

# Check if stack exists and get existing functions
existing_functions = []
try:
    # Get existing Lambda functions for this stage
    result = subprocess.run([
        'aws', 'lambda', 'list-functions', '--region', 'us-east-1',
        '--query', f'Functions[?ends_with(FunctionName, \`-{stage}\`)].FunctionName',
        '--output', 'json'
    ], capture_output=True, text=True, check=True)

    existing_lambda_functions = json.loads(result.stdout)

    # Map Lambda function names back to serverless function names
    for lambda_func in existing_lambda_functions:
        # Remove the stage suffix to get the original function name
        if lambda_func.endswith(f'-{stage}'):
            original_name = lambda_func[:-len(f'-{stage}')]
            if original_name in config.get('functions', {}):
                existing_functions.append(original_name)

    print(f"Found existing functions: {existing_functions}")
except:
    print("No existing functions found or error checking")
    pass

# Get stage-specific environment configuration from main config
stage_env = config.get('custom', {}).get('environments', {}).get(stage, {})

# Create minimal serverless config using proper stage-specific settings
minimal_config = {
    'service': 'thyview-api',
    'provider': {
        'name': 'aws',
        'runtime': 'nodejs20.x',
        'region': config.get('custom', {}).get('region', 'us-east-1'),
        'stage': stage,
        'memorySize': stage_env.get('memorySize', 1024),
        'timeout': stage_env.get('timeout', 30),
        'logRetentionInDays': stage_env.get('logRetentionInDays', 14),
        'apiName': f'thyview-api-{stage}',
        'stackName': f'thyview-api-{stage}',
        'stackTags': {
            'Project': 'ThyView',
            'Environment': stage,
            'Service': 'thyview-api',
            'ManagedBy': 'Serverless'
        },
        'environment': {
            'NODE_ENV': stage,
            'STAGE': stage,
            'TABLE_PREFIX': '',
            'BUCKET_NAME': f'thyview-storage-{stage}',
            'TMDB_API_KEY': '\${env:TMDB_API_KEY, "f96e6f617641625fd2b719874603a237"}',
            'HUGGING_FACE_TOKEN': '\${env:HUGGING_FACE_TOKEN, "*************************************"}',
            'REDIS_URL': config.get('provider', {}).get('environment', {}).get('REDIS_URL', ''),
            'REDIS_HOST': stage_env.get('redisHost', 'localhost'),
            'REDIS_PORT': stage_env.get('redisPort', '6379'),
            'REDIS_PASSWORD': '\${env:REDIS_PASSWORD, "your_redis_password"}',
            'SEARCH_LOGS_TABLE': f'searchlogs-{stage}',
            'PROFILE_PICTURE_BUCKET': f'thyview-profile-pictures-{stage}'
        },
        'iam': {
            'role': {
                'statements': [
                    {
                        'Effect': 'Allow',
                        'Action': ['dynamodb:*', 's3:*', 'logs:*'],
                        'Resource': '*'
                    }
                ]
            }
        }
    },
    'plugins': [
        'serverless-esbuild',
        'serverless-dotenv-plugin'
    ],
    'custom': {
        'stage': stage,
        'region': config.get('custom', {}).get('region', 'us-east-1'),
        'environments': config.get('custom', {}).get('environments', {}),
        'esbuild': config.get('custom', {}).get('esbuild', {
            'bundle': True,
            'minify': False,
            'sourcemap': True,
            'target': 'node20',
            'platform': 'node',
            'exclude': ['aws-sdk']
        })
    },
    'functions': {
        'firebaseAuthorizer': {
            'handler': 'functions/src/authorizer/firebaseAuthorizer.handler',
            'name': f'firebaseAuthorizer-{stage}'
        }
    }
}

# Add existing functions to the config
for existing_func in existing_functions:
    if existing_func != function_name and existing_func != 'firebaseAuthorizer':
        minimal_config['functions'][existing_func] = config['functions'][existing_func]

# Add the new function
minimal_config['functions'][function_name] = func_config

# Add basic resources (search logs table for movie functions, basic tables for others)
resources = {
    'Resources': {
        'SearchLogsTable': {
            'Type': 'AWS::DynamoDB::Table',
            'Properties': {
                'TableName': f'searchlogs-{stage}',
                'BillingMode': 'PAY_PER_REQUEST',
                'AttributeDefinitions': [
                    {'AttributeName': 'id', 'AttributeType': 'S'}
                ],
                'KeySchema': [
                    {'AttributeName': 'id', 'KeyType': 'HASH'}
                ]
            }
        }
    }
}

# Add function-specific resources based on function type
if 'user' in function_name.lower():
    # User-related functions need users table
    resources['Resources']['UsersTable'] = {
        'Type': 'AWS::DynamoDB::Table',
        'Properties': {
            'TableName': f'users-{stage}',
            'BillingMode': 'PAY_PER_REQUEST',
            'AttributeDefinitions': [
                {'AttributeName': 'id', 'AttributeType': 'S'},
                {'AttributeName': 'username', 'AttributeType': 'S'},
                {'AttributeName': 'email', 'AttributeType': 'S'},
                {'AttributeName': 'externalUserId', 'AttributeType': 'S'}
            ],
            'KeySchema': [
                {'AttributeName': 'id', 'KeyType': 'HASH'}
            ],
            'GlobalSecondaryIndexes': [
                {
                    'IndexName': 'username-index',
                    'KeySchema': [
                        {'AttributeName': 'username', 'KeyType': 'HASH'}
                    ],
                    'Projection': {'ProjectionType': 'ALL'}
                },
                {
                    'IndexName': 'email-index',
                    'KeySchema': [
                        {'AttributeName': 'email', 'KeyType': 'HASH'}
                    ],
                    'Projection': {'ProjectionType': 'ALL'}
                },
                {
                    'IndexName': 'externalUserId-index',
                    'KeySchema': [
                        {'AttributeName': 'externalUserId', 'KeyType': 'HASH'}
                    ],
                    'Projection': {'ProjectionType': 'ALL'}
                }
            ]
        }
    }

elif function_name.startswith(('create', 'get', 'edit', 'delete', 'like')) and 'post' in function_name.lower():
    # Post-related functions need posts and post_likes tables
    resources['Resources']['PostsTable'] = {
        'Type': 'AWS::DynamoDB::Table',
        'Properties': {
            'TableName': f'posts-{stage}',
            'BillingMode': 'PAY_PER_REQUEST',
            'AttributeDefinitions': [
                {'AttributeName': 'id', 'AttributeType': 'S'}
            ],
            'KeySchema': [
                {'AttributeName': 'id', 'KeyType': 'HASH'}
            ]
        }
    }
    resources['Resources']['PostLikesTable'] = {
        'Type': 'AWS::DynamoDB::Table',
        'Properties': {
            'TableName': f'postlikes-{stage}',
            'BillingMode': 'PAY_PER_REQUEST',
            'AttributeDefinitions': [
                {'AttributeName': 'id', 'AttributeType': 'S'}
            ],
            'KeySchema': [
                {'AttributeName': 'id', 'KeyType': 'HASH'}
            ]
        }
    }

elif 'article' in function_name.lower():
    # Article-related functions need articles and article_likes tables
    resources['Resources']['ArticlesTable'] = {
        'Type': 'AWS::DynamoDB::Table',
        'Properties': {
            'TableName': f'articles-{stage}',
            'BillingMode': 'PAY_PER_REQUEST',
            'AttributeDefinitions': [
                {'AttributeName': 'id', 'AttributeType': 'S'}
            ],
            'KeySchema': [
                {'AttributeName': 'id', 'KeyType': 'HASH'}
            ]
        }
    }
    resources['Resources']['ArticleLikesTable'] = {
        'Type': 'AWS::DynamoDB::Table',
        'Properties': {
            'TableName': f'articlelikes-{stage}',
            'BillingMode': 'PAY_PER_REQUEST',
            'AttributeDefinitions': [
                {'AttributeName': 'id', 'AttributeType': 'S'}
            ],
            'KeySchema': [
                {'AttributeName': 'id', 'KeyType': 'HASH'}
            ]
        }
    }

elif 'comment' in function_name.lower():
    # Comment-related functions need comments and comment_likes tables
    resources['Resources']['CommentsTable'] = {
        'Type': 'AWS::DynamoDB::Table',
        'Properties': {
            'TableName': f'comments-{stage}',
            'BillingMode': 'PAY_PER_REQUEST',
            'AttributeDefinitions': [
                {'AttributeName': 'id', 'AttributeType': 'S'}
            ],
            'KeySchema': [
                {'AttributeName': 'id', 'KeyType': 'HASH'}
            ]
        }
    }
    resources['Resources']['CommentLikesTable'] = {
        'Type': 'AWS::DynamoDB::Table',
        'Properties': {
            'TableName': f'commentlikes-{stage}',
            'BillingMode': 'PAY_PER_REQUEST',
            'AttributeDefinitions': [
                {'AttributeName': 'id', 'AttributeType': 'S'}
            ],
            'KeySchema': [
                {'AttributeName': 'id', 'KeyType': 'HASH'}
            ]
        }
    }

# Add S3 bucket for upload functions
if 'upload' in function_name.lower() or 'image' in function_name.lower():
    resources['Resources']['StorageBucket'] = {
        'Type': 'AWS::S3::Bucket',
        'Properties': {
            'BucketName': f'thyview-storage-{stage}',
            'CorsConfiguration': {
                'CorsRules': [{
                    'AllowedHeaders': ['*'],
                    'AllowedMethods': ['GET', 'PUT', 'POST', 'DELETE', 'HEAD'],
                    'AllowedOrigins': ['*'],
                    'MaxAge': 3000
                }]
            }
        }
    }

minimal_config['resources'] = resources

# Write to temporary config file
with open('$TEMP_CONFIG', 'w') as f:
    yaml.dump(minimal_config, f, default_flow_style=False, sort_keys=False)

print(f"SUCCESS: Created temporary config for {function_name}")
EOF

        # Check if Python script succeeded
        if [ $? -ne 0 ]; then
            print_error "Failed to extract function configuration"
            rm -f "$TEMP_CONFIG"
            exit 1
        fi

        # Deploy using the temporary config
        print_status "Deploying only $FUNCTION_NAME function..."
        DEPLOY_CMD="serverless deploy --config $TEMP_CONFIG --stage $STAGE"

        print_status "Running: $DEPLOY_CMD"
        if eval $DEPLOY_CMD; then
            print_success "Function '$FUNCTION_NAME' deployed successfully to $STAGE!"

            # Show function info
            print_status "Function information:"
            serverless info --config $TEMP_CONFIG --stage $STAGE

            # Show useful commands
            print_status "Useful commands:"
            echo "  View logs: serverless logs --config $TEMP_CONFIG --function $FUNCTION_NAME --stage $STAGE --tail"
            echo "  Test function: serverless invoke --config $TEMP_CONFIG --function $FUNCTION_NAME --stage $STAGE"
            echo "  Function name in AWS: $FUNCTION_NAME-$STAGE"

            rm -f "$TEMP_CONFIG"
        else
            print_error "Single function deployment failed!"
            rm -f "$TEMP_CONFIG"
            exit 1
        fi
    fi
fi

print_success "Deployment completed successfully!"
