// src/index.ts
// Centralized exports for all AWS Lambda functions
// This file simplifies importing functions in the serverless.yml configuration

// Posts
export { createPost } from './reviews/posts/createPost';
export { getPostsList } from './reviews/posts/getPostsList';
export { getPostDetail } from './reviews/posts/getPostDetail';
export { editPost } from './reviews/posts/editPost';
export { deletePost } from './reviews/posts/deletePost';
export { likePost } from './reviews/posts/likePost';
export { hasUserLiked } from './reviews/posts/hasUserLiked';
export { getImageUploadUrl } from './reviews/posts/getImageUploadUrl';

// Comments
export { addComment } from './reviews/comments/addComment';
export { getReplies } from './reviews/comments/getReplies';
export { deleteComment } from './reviews/comments/deleteComment';

// Reports
export { reportPost } from './reviews/reports/reportPost';
export { reportComment } from './reviews/reports/reportComment';

// Articles
export { handler as createArticle } from './articles/createArticle';
export { handler as getArticles } from './articles/getArticles';
export { handler as getArticle } from './articles/getArticle';
export { handler as updateArticle } from './articles/updateArticle';
export { handler as deleteArticle } from './articles/deleteArticle';
export { handler as likeArticle } from './articles/likeArticle';
export { handler as unlikeArticle } from './articles/unlikeArticle';
export { handler as checkArticleLiked } from './articles/checkArticleLiked';
export { handler as getUserLikedArticles } from './articles/getUserLikedArticles';
export { handler as getArticleLikers } from './articles/getArticleLikers';

// Watchlist
export { addWatchlist } from './watchlist/addWatchlist';
export { removeWatchlist } from './watchlist/removeWatchlist';
export { getWatchlist } from './watchlist/getWatchlist';

// Export types and models for use by other modules
export * from './common/types/articles-types';
export * from './common/types/watchlist-types';
export * from './articles/articles-models';
export * from './articles/articles-repository';
export * from './articles/articles-services';