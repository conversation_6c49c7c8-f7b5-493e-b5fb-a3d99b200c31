// src/watchlist/services/WatchlistService.ts
import { WatchlistModel, WatchlistDocument, generateWatchlistId } from '../../common/models/WatchlistModel';
import { 
  IWatchlist, 
  AddWatchlistRequest, 
  UpdateWatchlistRequest, 
  GetWatchlistQuery,
  WatchlistStatus,
  PriorityTag 
} from '../../common/types/watchlist-types';
import { Logger } from '../../common/utils/logger';

export class WatchlistService {
  private logger: Logger;

  constructor() {
    this.logger = Logger.getInstance('WatchlistService');
  }

  /**
   * Add a movie to user's watchlist
   */
  async addToWatchlist(firebaseUid: string, request: AddWatchlistRequest): Promise<WatchlistDocument> {
    try {
      this.logger.info('Adding movie to watchlist', { firebaseUid, movieId: request.movieId });

      // Validate required fields
      this.validateAddRequest(request);

      // Generate composite key
      const id = generateWatchlistId(firebaseUid, request.movieId);

      // Check if item already exists
      const existingItem = await this.getWatchlistItem(firebaseUid, request.movieId);
      if (existingItem) {
        throw new Error('Movie already exists in watchlist');
      }

      // Create new watchlist item
      const watchlistItem = new WatchlistModel({
        id,
        firebaseUid,
        name: request.name.trim(),
        movieId: request.movieId,
        status: request.status || WatchlistStatus.WATCH,
        liked: request.liked,
        priorityTag: request.priorityTag,
      });

      const savedItem = await watchlistItem.save();
      this.logger.info('Movie added to watchlist successfully', { id: (savedItem as any).id });

      return savedItem as WatchlistDocument;
    } catch (error) {
      this.logger.error('Failed to add movie to watchlist', error, { firebaseUid, movieId: request.movieId });
      throw error;
    }
  }

  /**
   * Remove a movie from user's watchlist
   */
  async removeFromWatchlist(firebaseUid: string, movieId: string): Promise<void> {
    try {
      this.logger.info('Removing movie from watchlist', { firebaseUid, movieId });

      const id = generateWatchlistId(firebaseUid, movieId);

      // Check if item exists
      const existingItem = await WatchlistModel.get(id);
      if (!existingItem) {
        throw new Error('Movie not found in watchlist');
      }

      await WatchlistModel.delete(id);
      this.logger.info('Movie removed from watchlist successfully', { id });
    } catch (error) {
      this.logger.error('Failed to remove movie from watchlist', error, { firebaseUid, movieId });
      throw error;
    }
  }

  /**
   * Get user's watchlist with optional filtering
   */
  async getWatchlist(firebaseUid: string, query: GetWatchlistQuery = {}): Promise<{
    items: WatchlistDocument[];
    lastEvaluatedKey?: string;
    count: number;
  }> {
    try {
      this.logger.info('Getting user watchlist', { firebaseUid, query });

      let queryBuilder = WatchlistModel.query('firebaseUid').eq(firebaseUid);

      // Apply filters
      if (query.status) {
        queryBuilder = queryBuilder.filter('status').eq(query.status);
      }

      if (query.priorityTag) {
        queryBuilder = queryBuilder.filter('priorityTag').eq(query.priorityTag);
      }

      // Apply pagination
      if (query.limit) {
        queryBuilder = queryBuilder.limit(query.limit);
      }

      if (query.lastEvaluatedKey) {
        queryBuilder = queryBuilder.startAt(JSON.parse(query.lastEvaluatedKey));
      }

      // Sort by createdAt descending (newest first)
      queryBuilder = queryBuilder.sort('descending');

      const result = await queryBuilder.exec();

      this.logger.info('Watchlist retrieved successfully', { 
        firebaseUid, 
        count: result.count,
        hasMore: !!result.lastKey 
      });

      return {
        items: result,
        lastEvaluatedKey: result.lastKey ? JSON.stringify(result.lastKey) : undefined,
        count: result.count,
      };
    } catch (error) {
      this.logger.error('Failed to get watchlist', error, { firebaseUid });
      throw error;
    }
  }

  /**
   * Get a specific watchlist item
   */
  async getWatchlistItem(firebaseUid: string, movieId: string): Promise<WatchlistDocument | null> {
    try {
      const id = generateWatchlistId(firebaseUid, movieId);
      const item = await WatchlistModel.get(id);
      return item || null;
    } catch (error) {
      if (error instanceof Error && error.name === 'DocumentNotFoundError') {
        return null;
      }
      this.logger.error('Failed to get watchlist item', error, { firebaseUid, movieId });
      throw error;
    }
  }

  /**
   * Update a watchlist item
   */
  async updateWatchlistItem(
    firebaseUid: string,
    movieId: string,
    updates: UpdateWatchlistRequest
  ): Promise<WatchlistDocument> {
    try {
      this.logger.info('Updating watchlist item', { firebaseUid, movieId, updates });

      const id = generateWatchlistId(firebaseUid, movieId);

      // Check if item exists
      const existingItem = await WatchlistModel.get(id);
      if (!existingItem) {
        throw new Error('Movie not found in watchlist');
      }

      // Apply updates
      const updateData: Partial<IWatchlist> = {};
      if (updates.status !== undefined) updateData.status = updates.status;
      if (updates.liked !== undefined) updateData.liked = updates.liked;
      if (updates.priorityTag !== undefined) updateData.priorityTag = updates.priorityTag;

      const updatedItem = await WatchlistModel.update({ id }, updateData);
      this.logger.info('Watchlist item updated successfully', { id });

      return updatedItem;
    } catch (error) {
      this.logger.error('Failed to update watchlist item', error, { firebaseUid, movieId });
      throw error;
    }
  }

  /**
   * Validate add request
   */
  private validateAddRequest(request: AddWatchlistRequest): void {
    if (!request.name || request.name.trim().length === 0) {
      throw new Error('Movie name is required');
    }

    if (!request.movieId || request.movieId.trim().length === 0) {
      throw new Error('Movie ID is required');
    }

    if (request.status && !Object.values(WatchlistStatus).includes(request.status)) {
      throw new Error('Invalid status value');
    }

    if (request.priorityTag && !Object.values(PriorityTag).includes(request.priorityTag)) {
      throw new Error('Invalid priority tag value');
    }
  }
}
