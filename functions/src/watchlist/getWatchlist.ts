// functions/src/watchlist/getWatchlist.ts
import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { WatchlistService } from './services/WatchlistService';
import { GetWatchlistQuery, WatchlistStatus, PriorityTag } from '../common/types/watchlist-types';
import { successResponse, errorResponse } from '../common/utils/response';
import { Logger } from '../common/utils/logger';

const logger = Logger.getInstance('getWatchlist');
const watchlistService = new WatchlistService();

/**
 * Lambda function to get user's watchlist with optional filtering and pagination
 * 
 * @param event - API Gateway event containing the request
 * @returns API Gateway response
 */
export const getWatchlist = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    logger.info('Get watchlist request received', { 
      path: event.path,
      httpMethod: event.httpMethod,
      queryStringParameters: event.queryStringParameters
    });

    // Extract Firebase UID from authorizer context
    const firebaseUid = event.requestContext?.authorizer?.uid;
    if (!firebaseUid) {
      logger.warn('Firebase UID missing from request context');
      return errorResponse(401, 'Unauthorized: Firebase UID missing');
    }

    // Parse query parameters
    const queryParams = event.queryStringParameters || {};
    const query: GetWatchlistQuery = {};

    // Parse status filter
    if (queryParams.status) {
      const status = queryParams.status as WatchlistStatus;
      if (Object.values(WatchlistStatus).includes(status)) {
        query.status = status;
      } else {
        return errorResponse(400, `Bad Request: Invalid status value. Must be one of: ${Object.values(WatchlistStatus).join(', ')}`);
      }
    }

    // Parse priority tag filter
    if (queryParams.priorityTag) {
      const priorityTag = queryParams.priorityTag as PriorityTag;
      if (Object.values(PriorityTag).includes(priorityTag)) {
        query.priorityTag = priorityTag;
      } else {
        return errorResponse(400, `Bad Request: Invalid priorityTag value. Must be one of: ${Object.values(PriorityTag).join(', ')}`);
      }
    }

    // Parse limit
    if (queryParams.limit) {
      const limit = parseInt(queryParams.limit, 10);
      if (isNaN(limit) || limit <= 0 || limit > 100) {
        return errorResponse(400, 'Bad Request: limit must be a positive integer between 1 and 100');
      }
      query.limit = limit;
    } else {
      query.limit = 20; // Default limit
    }

    // Parse pagination token
    if (queryParams.lastEvaluatedKey) {
      try {
        // Validate that it's valid JSON
        JSON.parse(queryParams.lastEvaluatedKey);
        query.lastEvaluatedKey = queryParams.lastEvaluatedKey;
      } catch (parseError) {
        return errorResponse(400, 'Bad Request: Invalid lastEvaluatedKey format');
      }
    }

    logger.info('Getting user watchlist', { 
      firebaseUid, 
      query 
    });

    // Get watchlist using service
    const result = await watchlistService.getWatchlist(firebaseUid, query);

    logger.info('Watchlist retrieved successfully', { 
      firebaseUid,
      count: result.count,
      hasMore: !!result.lastEvaluatedKey
    });

    // Return success response
    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
        'Access-Control-Allow-Methods': 'GET,OPTIONS'
      },
      body: JSON.stringify({
        success: true,
        data: result.items,
        count: result.count,
        lastEvaluatedKey: result.lastEvaluatedKey,
        hasMore: !!result.lastEvaluatedKey
      })
    };

  } catch (error) {
    logger.error('Get watchlist operation failed', error, {
      path: event.path,
      httpMethod: event.httpMethod,
      queryStringParameters: event.queryStringParameters
    });

    // Generic server error
    return errorResponse(500, 'Internal Server Error: Failed to retrieve watchlist');
  }
};
