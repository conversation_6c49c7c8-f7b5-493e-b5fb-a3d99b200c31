// functions/src/watchlist/removeWatchlist.ts
import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { WatchlistService } from './services/WatchlistService';
import { successResponse, errorResponse } from '../common/utils/response';
import { Logger } from '../common/utils/logger';

const logger = Logger.getInstance('removeWatchlist');
const watchlistService = new WatchlistService();

/**
 * Lambda function to remove a movie from user's watchlist
 * 
 * @param event - API Gateway event containing the request
 * @returns API Gateway response
 */
export const removeWatchlist = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    logger.info('Remove watchlist request received', { 
      path: event.path,
      httpMethod: event.httpMethod,
      pathParameters: event.pathParameters
    });

    // Extract Firebase UID from authorizer context
    const firebaseUid = event.requestContext?.authorizer?.uid;
    if (!firebaseUid) {
      logger.warn('Firebase UID missing from request context');
      return errorResponse(401, 'Unauthorized: Firebase UID missing');
    }

    // Extract movieId from path parameters
    const movieId = event.pathParameters?.movieId;
    if (!movieId) {
      logger.warn('MovieId missing from path parameters');
      return errorResponse(400, 'Bad Request: movieId is required in path');
    }

    // Validate movieId
    const trimmedMovieId = movieId.trim();
    if (trimmedMovieId.length === 0) {
      return errorResponse(400, 'Bad Request: movieId cannot be empty');
    }

    logger.info('Removing movie from watchlist', { 
      firebaseUid, 
      movieId: trimmedMovieId 
    });

    // Remove movie from watchlist using service
    await watchlistService.removeFromWatchlist(firebaseUid, trimmedMovieId);

    logger.info('Movie removed from watchlist successfully', { 
      firebaseUid,
      movieId: trimmedMovieId 
    });

    // Return success response
    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
        'Access-Control-Allow-Methods': 'DELETE,OPTIONS'
      },
      body: JSON.stringify({
        success: true,
        message: 'Movie removed from watchlist successfully'
      })
    };

  } catch (error) {
    logger.error('Remove watchlist operation failed', error, {
      path: event.path,
      httpMethod: event.httpMethod,
      pathParameters: event.pathParameters
    });

    // Handle specific error types
    if (error instanceof Error) {
      if (error.message === 'Movie not found in watchlist') {
        return errorResponse(404, 'Not Found: Movie not found in watchlist');
      }
    }

    // Generic server error
    return errorResponse(500, 'Internal Server Error: Failed to remove movie from watchlist');
  }
};
