// functions/src/watchlist/addWatchlist.ts
import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { WatchlistService } from './services/WatchlistService';
import { AddWatchlistRequest } from '../common/types/watchlist-types';
import { successResponse, errorResponse } from '../common/utils/response';
import { Logger } from '../common/utils/logger';

const logger = Logger.getInstance('addWatchlist');
const watchlistService = new WatchlistService();

/**
 * Lambda function to add a movie to user's watchlist
 * 
 * @param event - API Gateway event containing the request
 * @returns API Gateway response
 */
export const addWatchlist = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    logger.info('Add watchlist request received', { 
      path: event.path,
      httpMethod: event.httpMethod,
      headers: event.headers
    });

    // Extract Firebase UID from authorizer context
    const firebaseUid = event.requestContext?.authorizer?.uid;
    if (!firebaseUid) {
      logger.warn('Firebase UID missing from request context');
      return errorResponse(401, 'Unauthorized: Firebase UID missing');
    }

    // Parse and validate request body
    if (!event.body) {
      logger.warn('Request body is missing');
      return errorResponse(400, 'Bad Request: Request body is required');
    }

    let requestBody: AddWatchlistRequest;
    try {
      requestBody = JSON.parse(event.body);
    } catch (parseError) {
      logger.error('Failed to parse request body', parseError);
      return errorResponse(400, 'Bad Request: Invalid JSON in request body');
    }

    // Validate required fields
    if (!requestBody.name || !requestBody.movieId) {
      logger.warn('Required fields missing', { requestBody });
      return errorResponse(400, 'Bad Request: name and movieId are required');
    }

    // Trim and validate name
    requestBody.name = requestBody.name.trim();
    if (requestBody.name.length === 0) {
      return errorResponse(400, 'Bad Request: name cannot be empty');
    }

    // Trim and validate movieId
    requestBody.movieId = requestBody.movieId.trim();
    if (requestBody.movieId.length === 0) {
      return errorResponse(400, 'Bad Request: movieId cannot be empty');
    }

    logger.info('Adding movie to watchlist', { 
      firebaseUid, 
      movieId: requestBody.movieId,
      movieName: requestBody.name 
    });

    // Add movie to watchlist using service
    const watchlistItem = await watchlistService.addToWatchlist(firebaseUid, requestBody);

    logger.info('Movie added to watchlist successfully', { 
      id: watchlistItem.id,
      firebaseUid,
      movieId: requestBody.movieId 
    });

    // Return success response
    return {
      statusCode: 201,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
        'Access-Control-Allow-Methods': 'POST,OPTIONS'
      },
      body: JSON.stringify({
        success: true,
        message: 'Movie added to watchlist successfully',
        data: watchlistItem
      })
    };

  } catch (error) {
    logger.error('Add watchlist operation failed', error, {
      path: event.path,
      httpMethod: event.httpMethod
    });

    // Handle specific error types
    if (error instanceof Error) {
      if (error.message === 'Movie already exists in watchlist') {
        return errorResponse(409, 'Conflict: Movie already exists in watchlist');
      }
      
      if (error.message.includes('required') || error.message.includes('Invalid')) {
        return errorResponse(400, `Bad Request: ${error.message}`);
      }
    }

    // Generic server error
    return errorResponse(500, 'Internal Server Error: Failed to add movie to watchlist');
  }
};
