// src/common/models/WatchlistModel.ts
import dynamoose, { Schema } from 'dynamoose';
import { Item } from 'dynamoose/dist/Item';
import { IWatchlist, WatchlistStatus, PriorityTag } from '../types/watchlist-types';

// Extend IWatchlist to include Dynamoose Item methods
export interface WatchlistDocument extends Item, IWatchlist {}

/**
 * Dynamoose schema for Watchlist table
 * Primary Key: Composite key (firebaseUid#movieId)
 * GSI1: firebaseUid (for querying user's watchlist)
 * GSI2: movieId (for querying movie across users)
 */
const watchlistSchema = new Schema({
  id: {
    type: String,
    hashKey: true,
    required: true,
    // Composite key format: firebaseUid#movieId
  },
  firebaseUid: {
    type: String,
    required: true,
    index: {
      type: 'global',
      name: 'firebaseUid-index',
      rangeKey: 'createdAt',
    },
  },
  name: {
    type: String,
    required: true,
  },
  movieId: {
    type: String,
    required: true,
    index: {
      type: 'global',
      name: 'movieId-index',
      rangeKey: 'createdAt',
    },
  },
  status: {
    type: String,
    required: true,
    enum: Object.values(WatchlistStatus),
    default: WatchlistStatus.WATCH,
  },
  liked: {
    type: Boolean,
    required: false,
  },
  priorityTag: {
    type: String,
    required: false,
    enum: Object.values(PriorityTag),
  },
}, {
  timestamps: true, // This automatically creates createdAt and updatedAt
});

// Create and export the model with environment-specific table name
export const WatchlistModel = dynamoose.model<WatchlistDocument>(`watchlist-${process.env.STAGE}`, watchlistSchema);

/**
 * Helper function to generate composite key
 */
export const generateWatchlistId = (firebaseUid: string, movieId: string): string => {
  return `${firebaseUid}#${movieId}`;
};

/**
 * Helper function to parse composite key
 */
export const parseWatchlistId = (id: string): { firebaseUid: string; movieId: string } => {
  const [firebaseUid, movieId] = id.split('#');
  return { firebaseUid, movieId };
};

// Export the schema for testing purposes
export { watchlistSchema };
