// src/common/types/watchlist-types.ts

/**
 * Enum for watchlist status values
 */
export enum WatchlistStatus {
  WATCH = 'watch',
  WATCHED = 'watched'
}

/**
 * Enum for priority tag values
 */
export enum PriorityTag {
  MOST_WANTED = 'mostWanted',
  WATCH_NEXT = 'watchNext',
  SOMEDAY = 'someday'
}

/**
 * Interface representing a Watchlist item
 */
export interface IWatchlist {
  id?: string; // Auto-generated composite key: firebaseUid#movieId
  firebaseUid: string; // Firebase UID from authorizer
  name: string; // Movie name
  movieId: string; // Movie ID
  createdAt?: string; // ISO timestamp
  updatedAt?: string; // ISO timestamp
  status: WatchlistStatus; // 'watch' or 'watched'
  liked?: boolean; // Optional liked flag
  priorityTag?: PriorityTag; // Optional priority tag
}

/**
 * Request interface for adding a watchlist item
 */
export interface AddWatchlistRequest {
  name: string;
  movieId: string;
  status: WatchlistStatus;
  liked?: boolean;
  priorityTag?: PriorityTag;
}

/**
 * Request interface for updating a watchlist item
 */
export interface UpdateWatchlistRequest {
  status?: WatchlistStatus;
  liked?: boolean;
  priorityTag?: PriorityTag;
}

/**
 * Request interface for removing a watchlist item
 */
export interface RemoveWatchlistRequest {
  movieId: string;
}

/**
 * Query parameters for getting watchlist items
 */
export interface GetWatchlistQuery {
  status?: WatchlistStatus;
  priorityTag?: PriorityTag;
  limit?: number;
  lastEvaluatedKey?: string;
}

/**
 * Response interface for watchlist operations
 */
export interface WatchlistResponse {
  success: boolean;
  data?: IWatchlist | IWatchlist[];
  message?: string;
  lastEvaluatedKey?: string;
  count?: number;
}
